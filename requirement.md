# 需求：在商详页（包括老poi页和一致性poi页）的领券栏和领券浮层中实现国补（国家补贴）功能

## 背景信息
- 涉及接口：com.dianping.api.picasso.controller.IssueCouponComponentAction#execute
- 技术方案文档：https://km.sankuai.com/collabpage/**********
- 国补门店标识接口文档：https://km.sankuai.com/collabpage/**********

## 具体需求

### 1. 触发条件
- 商详页必须有国补门店标识（一期已支持）
- 对所有用户展示国补氛围，无需校验GPS定位

### 2. 展位配置信息
- 名称：美团-商详页国补氛围条
- 标识：MT_MERCHANT_DETAILS_BANNER
- 标签组名称：国补氛围条
- 标签值名称：国家补贴
- 标签值ID：21857

### 3. 功能实现要求

#### 3.1 领券栏-国补标签

**展示逻辑：**
- 根据不同类目对应的补贴比例展示15%或20%的国补标签
- 标签位次（从左往右）：国补 > 神券包 > 神券 > 其他优惠券

**文案规则：**
- 当用户在该门店至少有1个品类未领取国补资格时：【国补 | 最高补贴xx% | 领取】（xx=未领取类目的最高补贴比例）
- 当用户领取了该门店所有品类的国补资格时：【国补 | 最高补贴xx% | 已领】（xx=已领取类目的最高补贴比例）

**交互行为：**
- 点击国补标签唤起领券浮层
- 点击时需要按压动效承接

#### 3.2 领券浮层-国补模块

**UI元素：**
- icon：【国家补贴】
- 优惠：根据不同类目对应的补贴比例展示【15%】或【20%】
- 注释：【最高补贴】
- 主标题：【国家补贴】
- 副标题：【领后使用 美团支付、云闪付可享】

**交互按钮逻辑：**
- 当用户在该门店至少有1个商品类目未领取国补资格、或闪购接口失效时：展示【立即领取】按钮
- 当用户领取了该门店所有商品类目的国补资格时：展示【已领取】按钮
- 所有按钮点击后均跳转至国补到店分会场

## 技术实现要求

请在IssueCouponComponentAction接口中实现上述功能，确保：
1. 正确调用ZDC门店标识接口获取国补门店信息
2. 根据用户的国补资格状态和类目补贴比例正确展示UI元素
3. 实现正确的交互逻辑和跳转行为
4. 保持与现有领券栏和浮层功能的兼容性