# 国补功能实现方案设计

## 1. 需求分析总结

### 1.1 核心需求
在 `IssueCouponComponentAction#execute` 接口中实现国补（国家补贴）功能，包括：
- **领券栏国补标签**：根据用户国补资格状态展示不同文案和状态
- **领券浮层国补模块**：展示详细的国补信息和交互按钮
- **优先级控制**：国补标签优先级最高（国补 > 神券包 > 神券 > 其他优惠券）

### 1.2 技术要求
- 调用ZDC门店标识接口获取国补门店信息
- 根据用户国补资格状态正确展示UI元素
- 保持与现有领券栏和浮层功能的兼容性
- 支持跳转至国补到店分会场

## 2. 现有架构分析

### 2.1 当前处理流程
```mermaid
graph TD
    A[IssueCouponComponentAction#execute] --> B[参数验证]
    B --> C[用户信息处理RestUserInfoService]
    C --> D[构建查询上下文PromotionRequestContext]
    D --> E[券查询服务CouponIssueActivityQueryService]
    E --> F[并行处理器链执行]
    F --> G[业务逻辑处理UnifiedIssueCouponBiz]
    G --> H[响应组装IssueCouponComponentDTO]
    
    F --> F1[TgcCouponProcessor]
    F --> F2[ResourcePromotionProcessor]
    F --> F3[MerchantAccountCouponProcessor]
    F --> F4[MagicalMemberCouponProcessor]
    F --> F5[GovernmentSubsidyProcessor]
    F --> F6[其他处理器...]
```

### 2.2 现有国补相关组件
- ✅ **GovernmentSubsidyProcessor**：已在处理器链中
- ✅ **ZdcShopIdentityService**：门店标识服务接口
- ✅ **GovernmentSubsidyInfo/Tag/Module**：数据结构已定义
- ❌ **处理器实现有误**：未正确实现AbstractPromoProcessor接口

## 3. 技术实现方案

### 3.1 修复GovernmentSubsidyProcessor实现

**问题识别**：
当前GovernmentSubsidyProcessor实现了错误的方法签名，应该实现`prePare`和`loadPromo`方法。

**解决方案**：
```java
@Component
@Slf4j
public class GovernmentSubsidyProcessor extends AbstractPromoProcessor {

    @Autowired
    private ZdcShopIdentityService zdcShopIdentityService;

    @Override
    public void prePare(final CouponActivityContext promoCtx, IMobileContext iMobileContext) {
        try {
            // 检查开关和必要参数
            if (!Lion.getBooleanValue(GOVERNMENT_SUBSIDY_SWITCH, false) || 
                promoCtx.getShopIdL() == null || promoCtx.getShopIdL() <= 0) {
                return;
            }

            // 异步查询国补信息
            Future<GovernmentSubsidyInfo> future = ExecutorService.submit(() -> {
                return zdcShopIdentityService.getGovernmentSubsidyInfo(
                    promoCtx.getShopIdL(), 
                    promoCtx.getUserId(), 
                    promoCtx.isMt()
                );
            });
            promoCtx.setGovernmentSubsidyInfoFuture(future);
            
        } catch (Exception e) {
            log.error("GovernmentSubsidyProcessor prepare error", e);
        }
    }

    @Override
    public void loadPromo(CouponActivityContext promoCtx) {
        try {
            Future<GovernmentSubsidyInfo> future = promoCtx.getGovernmentSubsidyInfoFuture();
            if (future != null) {
                int timeout = Lion.getIntValue("mapi-pay-promo-web.government.subsidy.timeout", 500);
                GovernmentSubsidyInfo subsidyInfo = future.get(timeout, TimeUnit.MILLISECONDS);
                
                if (subsidyInfo != null && subsidyInfo.isShowGovernmentSubsidy()) {
                    promoCtx.setGovernmentSubsidyInfo(subsidyInfo);
                    Cat.logEvent("GovernmentSubsidy", "Show");
                }
            }
        } catch (Exception e) {
            log.error("GovernmentSubsidyProcessor loadPromo error", e);
        }
    }

    @Override
    public String getPromoName() {
        return "governmentSubsidy";
    }
}
```

### 3.2 完善ZdcShopIdentityService实现

**当前问题**：
- ZDC接口调用为TODO状态
- 用户国补资格查询为模拟数据

**实现方案**：
```java
@Service
@Slf4j
public class ZdcShopIdentityServiceImpl implements ZdcShopIdentityService {

    @Autowired
    private ZdcApiClient zdcApiClient; // 需要新增ZDC接口客户端
    
    @Autowired
    private FlashSaleApiClient flashSaleApiClient; // 需要新增闪购接口客户端

    @Override
    public GovernmentSubsidyInfo getGovernmentSubsidyInfo(Long shopId, Long userId, boolean isMt) {
        try {
            // 1. 查询门店是否有国补标识
            if (!hasGovernmentSubsidyIdentity(shopId, isMt)) {
                return null;
            }

            // 2. 查询用户国补资格状态
            SubsidyQualificationInfo qualificationInfo = queryUserSubsidyQualification(shopId, userId, isMt);
            
            // 3. 构建返回信息
            GovernmentSubsidyInfo subsidyInfo = new GovernmentSubsidyInfo();
            subsidyInfo.setShowGovernmentSubsidy(true);
            subsidyInfo.setSubsidyTag(buildSubsidyTag(qualificationInfo));
            subsidyInfo.setSubsidyModule(buildSubsidyModule(qualificationInfo));
            
            return subsidyInfo;
            
        } catch (Exception e) {
            log.error("获取门店国补信息失败", e);
            return null;
        }
    }

    private boolean callZdcApi(Long shopId, boolean isMt) {
        try {
            // 调用ZDC门店标识接口
            ZdcShopIdentityRequest request = new ZdcShopIdentityRequest();
            request.setShopId(shopId);
            request.setIdentityType("GOVERNMENT_SUBSIDY");
            request.setPlatform(isMt ? "MT" : "DP");
            
            ZdcShopIdentityResponse response = zdcApiClient.queryShopIdentity(request);
            return response != null && response.isSuccess() && response.hasIdentity();
            
        } catch (Exception e) {
            log.error("调用ZDC接口失败, shopId: {}", shopId, e);
            return false;
        }
    }

    private SubsidyQualificationInfo queryUserSubsidyQualification(Long shopId, Long userId, boolean isMt) {
        try {
            // 调用闪购接口查询用户国补资格
            FlashSaleQualificationRequest request = new FlashSaleQualificationRequest();
            request.setShopId(shopId);
            request.setUserId(userId);
            request.setPlatform(isMt ? "MT" : "DP");
            
            FlashSaleQualificationResponse response = flashSaleApiClient.queryQualification(request);
            
            SubsidyQualificationInfo info = new SubsidyQualificationInfo();
            if (response != null && response.isSuccess()) {
                info.setMaxUnclaimedRate(response.getMaxUnclaimedRate());
                info.setMaxClaimedRate(response.getMaxClaimedRate());
                info.setHasUnclaimedCategory(response.hasUnclaimedCategory());
            } else {
                // 降级处理：接口失败时的默认值
                info.setMaxUnclaimedRate("20%");
                info.setMaxClaimedRate("15%");
                info.setHasUnclaimedCategory(true);
            }
            return info;
            
        } catch (Exception e) {
            log.error("查询用户国补资格失败", e);
            // 降级处理
            SubsidyQualificationInfo info = new SubsidyQualificationInfo();
            info.setMaxUnclaimedRate("20%");
            info.setHasUnclaimedCategory(true);
            return info;
        }
    }
}
```

### 3.3 扩展CouponActivityContext数据结构

**需要新增的字段**：
```java
public class CouponActivityContext {
    // 现有字段...
    
    // 新增国补相关字段
    private Future<GovernmentSubsidyInfo> governmentSubsidyInfoFuture;
    private GovernmentSubsidyInfo governmentSubsidyInfo;
    
    // getter/setter方法
}
```

### 3.4 修改UnifiedIssueCouponBiz处理逻辑

**关键修改点**：
1. **标签优先级排序**：确保国补标签排在最前面
2. **浮层模块集成**：将国补模块添加到浮层中

```java
public class UnifiedIssueCouponBiz {
    
    public IssueCouponComponentDTO toIssueCouponComponentResponse(
            CouponActivityContext couponActivityContext,
            PromotionRequestContext promotionRequestContext, 
            IMobileContext context) {
        
        // 现有逻辑...
        
        // 获取国补信息
        GovernmentSubsidyInfo governmentSubsidyInfo = couponActivityContext.getGovernmentSubsidyInfo();
        
        // 构建简单组件（领券栏）
        IssueCouponSimpleComponentDetail simpleComponent = buildSimpleComponent(
            issueCouponActivitieList, 
            couponExposeDTOS,
            shopResourcePromotionDO,
            governmentSubsidyInfo  // 新增参数
        );
        
        // 构建详细组件（浮层）
        IssueCouponComponentDetail detailComponent = buildDetailComponent(
            issueCouponActivitieList,
            couponExposeDTOS, 
            shopResourcePromotionDO,
            governmentSubsidyInfo  // 新增参数
        );
        
        // 组装返回结果...
    }
    
    private IssueCouponSimpleComponentDetail buildSimpleComponent(
            List<IssueCouponActivity> activities,
            List<CouponExposeDTO> coupons,
            ShopResourcePromotionDO shopResource,
            GovernmentSubsidyInfo governmentSubsidy) {
        
        List<IssueCouponSimpleDetail> couponList = new ArrayList<>();
        
        // 1. 优先添加国补标签（如果存在）
        if (governmentSubsidy != null && governmentSubsidy.isShowGovernmentSubsidy()) {
            IssueCouponSimpleDetail subsidyTag = buildGovernmentSubsidySimpleDetail(governmentSubsidy);
            couponList.add(subsidyTag);
        }
        
        // 2. 添加神券包标签
        // 3. 添加神券标签  
        // 4. 添加其他优惠券
        // ... 现有逻辑
        
        return simpleComponent;
    }
    
    private IssueCouponComponentDetail buildDetailComponent(
            List<IssueCouponActivity> activities,
            List<CouponExposeDTO> coupons,
            ShopResourcePromotionDO shopResource,
            GovernmentSubsidyInfo governmentSubsidy) {
        
        IssueCouponComponentDetail detailComponent = new IssueCouponComponentDetail();
        
        // 设置国补氛围信息
        if (governmentSubsidy != null && governmentSubsidy.isShowGovernmentSubsidy()) {
            detailComponent.setGovernmentSubsidyInfo(governmentSubsidy);
        }
        
        // ... 现有逻辑
        
        return detailComponent;
    }
    
    private IssueCouponSimpleDetail buildGovernmentSubsidySimpleDetail(GovernmentSubsidyInfo subsidyInfo) {
        IssueCouponSimpleDetail simpleDetail = new IssueCouponSimpleDetail();
        GovernmentSubsidyTag tag = subsidyInfo.getSubsidyTag();
        
        simpleDetail.setTagText(tag.getTagText());
        simpleDetail.setTagType("GOVERNMENT_SUBSIDY");
        simpleDetail.setPriority(1); // 最高优先级
        simpleDetail.setSubsidyRate(tag.getSubsidyRate());
        simpleDetail.setTagStatus(tag.getTagStatus());
        
        return simpleDetail;
    }
}
```

## 4. 数据流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Action as IssueCouponComponentAction
    participant QueryService as CouponIssueActivityQueryService
    participant GSProcessor as GovernmentSubsidyProcessor
    participant ZdcService as ZdcShopIdentityService
    participant ZdcApi as ZDC接口
    participant FlashSaleApi as 闪购接口
    participant UnifiedBiz as UnifiedIssueCouponBiz

    Client->>Action: 请求领券组件
    Action->>QueryService: queryShopPromotions
    QueryService->>GSProcessor: prePare (异步)
    GSProcessor->>ZdcService: getGovernmentSubsidyInfo
    ZdcService->>ZdcApi: 查询门店国补标识
    ZdcApi-->>ZdcService: 返回标识结果
    ZdcService->>FlashSaleApi: 查询用户国补资格
    FlashSaleApi-->>ZdcService: 返回资格信息
    ZdcService-->>GSProcessor: 返回国补信息
    QueryService->>GSProcessor: loadPromo
    GSProcessor-->>QueryService: 设置国补信息到Context
    QueryService-->>Action: 返回CouponActivityContext
    Action->>UnifiedBiz: toIssueCouponComponentResponse
    UnifiedBiz-->>Action: 返回组装后的DTO
    Action-->>Client: 返回领券组件数据
```

## 5. 新增模块设计

### 5.1 ZDC接口客户端
```java
@Component
public class ZdcApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public ZdcShopIdentityResponse queryShopIdentity(ZdcShopIdentityRequest request) {
        // 实现ZDC接口调用
    }
}
```

### 5.2 闪购接口客户端
```java
@Component  
public class FlashSaleApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public FlashSaleQualificationResponse queryQualification(FlashSaleQualificationRequest request) {
        // 实现闪购接口调用
    }
}
```

### 5.3 配置管理
```properties
# Lion配置
mapi-pay-promo-web.government.subsidy.switch=true
mapi-pay-promo-web.government.subsidy.timeout=500
mapi-pay-promo-web.government.subsidy.jump.url=https://i.meituan.com/awp/hfe/block/dd8b8c9b/index.html
```

## 6. 代码改动点总结

### 6.1 修改文件
1. **GovernmentSubsidyProcessor.java** - 修复处理器实现
2. **ZdcShopIdentityServiceImpl.java** - 完善服务实现
3. **CouponActivityContext.java** - 新增国补相关字段
4. **UnifiedIssueCouponBiz.java** - 集成国补逻辑
5. **IssueCouponSimpleDetail.java** - 可能需要新增字段

### 6.2 新增文件
1. **ZdcApiClient.java** - ZDC接口客户端
2. **FlashSaleApiClient.java** - 闪购接口客户端
3. **ZdcShopIdentityRequest/Response.java** - ZDC接口数据结构
4. **FlashSaleQualificationRequest/Response.java** - 闪购接口数据结构

## 7. 风险点说明

### 7.1 技术风险
- **外部接口依赖**：ZDC和闪购接口的稳定性和性能
- **超时控制**：需要合理设置超时时间，避免影响整体响应
- **降级策略**：外部接口失败时的降级处理

### 7.2 业务风险
- **优先级冲突**：国补标签与现有标签的排序逻辑
- **兼容性问题**：新功能对现有功能的影响
- **数据一致性**：用户资格状态的实时性

### 7.3 缓解措施
1. **完善监控**：添加详细的性能监控和异常告警
2. **灰度发布**：通过Lion开关控制功能上线
3. **降级机制**：外部接口异常时的默认处理
4. **充分测试**：单元测试、集成测试、压力测试

## 8. 实施建议

### 8.1 开发阶段
1. **第一阶段**：修复GovernmentSubsidyProcessor，完善基础数据结构
2. **第二阶段**：实现ZDC和闪购接口集成
3. **第三阶段**：集成到UnifiedIssueCouponBiz，完善UI逻辑

### 8.2 测试策略
1. **单元测试**：各个组件的独立测试
2. **集成测试**：端到端流程测试
3. **性能测试**：并发场景下的性能验证
4. **兼容性测试**：确保不影响现有功能

### 8.3 上线策略
1. **灰度发布**：通过Lion开关控制功能开启
2. **监控告警**：实时监控接口性能和异常
3. **快速回滚**：出现问题时快速关闭功能

---

*本方案基于现有架构设计，确保与当前系统的兼容性，同时提供完整的国补功能实现路径。*
